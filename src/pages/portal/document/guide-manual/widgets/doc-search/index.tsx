import { useContext } from '@nuxtjs/composition-api';
import { Input } from 'ant-design-vue';
import Fuse from 'fuse.js';
import { defineComponent, onMounted, ref } from 'vue';
import { FLAT_MENU_CONFIG } from '../../config/guide-manual.config';
import { stripMarkdown } from '@/utils/strip-markdown';

const DocSearch = defineComponent({
  name: 'DocSearch',
  props: {},
  setup() {
    const { $content } = useContext();

    const fuseHandler = ref(null);
    const searchList = ref([]);
    const initSearch = async () => {
      const searchData = await $content('manuals', { text: true }).only(['text', 'slug']).fetch<any>();
      const stripedData = searchData.map((item) => {
        return {
          ...item,
          text: stripMarkdown(item.text),
        };
      });
      console.log(stripedData);
      const fuse = new Fuse(stripedData, {
        keys: ['text'],
        includeMatches: true,
        minMatchCharLength: 2,
      });

      fuseHandler.value = fuse;
    };

    const handleSearch = (keywords) => {
      const res = fuseHandler.value.search(keywords);
      console.log(res);
      searchList.value = res.map((v) => {
        return {
          ...v.item,
          title: FLAT_MENU_CONFIG.find((menu) => menu.key === v.item.slug).label,
        };
      });
    };

    onMounted(() => {
      initSearch();
    });
    return {
      handleSearch,
      searchList,
    };
  },
  render() {
    return (
      <div>
        <Input.Search placeholder="请输入内容关键词，如：准入排查、招标排查" onSearch={this.handleSearch} />
        <ul>
          {this.searchList.map((item) => {
            return (
              <li key={item.slug}>
                <nuxt-link to={item.slug}>{item.title}</nuxt-link>
              </li>
            );
          })}
        </ul>
      </div>
    );
  },
});

export default DocSearch;
